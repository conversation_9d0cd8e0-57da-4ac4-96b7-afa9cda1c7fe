#!/bin/bash

# Azure DevOps Agent Cache Clearing Script
# Purpose: Clear corrupted agent cache on dl01mds6nckqap1.bdx.com
# Author: <PERSON><PERSON><PERSON><PERSON>
# Date: July 22, 2025

set -e  # Exit on any error

# Default values
AGENT_PATH="/home/<USER>/agent1"
BACKUP_PATH="/home/<USER>/cache_backup_$(date +%Y%m%d_%H%M%S)"
CLEAR_CACHE=false
RESTART_SERVICE=false
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to log actions
log_action() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$BACKUP_PATH/agent_maintenance.log"
    if [ "$VERBOSE" = true ]; then
        print_status "$1"
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -a PATH    Agent path (default: /home/<USER>/agent1)"
    echo "  -b PATH    Backup path (default: /home/<USER>/cache_backup_TIMESTAMP)"
    echo "  -c         Clear cache directories"
    echo "  -r         Restart agent service after clearing"
    echo "  -v         Verbose output"
    echo "  -h         Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 -a /home/<USER>/agent1 -b /tmp/backup -c -r -v"
}

# Parse command line arguments
while getopts "a:b:crvh" opt; do
    case $opt in
        a) AGENT_PATH="$OPTARG" ;;
        b) BACKUP_PATH="$OPTARG" ;;
        c) CLEAR_CACHE=true ;;
        r) RESTART_SERVICE=true ;;
        v) VERBOSE=true ;;
        h) show_usage; exit 0 ;;
        \?) echo "Invalid option -$OPTARG" >&2; show_usage; exit 1 ;;
    esac
done

# Validate inputs
if [ ! -d "$AGENT_PATH" ]; then
    print_error "Agent path does not exist: $AGENT_PATH"
    exit 1
fi

# Create backup directory
mkdir -p "$BACKUP_PATH"
log_action "Starting Azure DevOps agent cache maintenance"
log_action "Agent path: $AGENT_PATH"
log_action "Backup path: $BACKUP_PATH"

print_status "Azure DevOps Agent Cache Maintenance"
print_status "Agent Path: $AGENT_PATH"
print_status "Backup Path: $BACKUP_PATH"

# Check if running as appropriate user
if [ "$EUID" -ne 0 ] && [ "$(whoami)" != "bdadmin" ]; then
    print_warning "Consider running as root or bdadmin user for full access"
fi

# Backup current state
print_status "Creating backup of current state..."

# Get directory structure
find "$AGENT_PATH/_work" -type d 2>/dev/null | head -100 > "$BACKUP_PATH/work_directory_structure.txt" || true

# Get disk usage before
df -h > "$BACKUP_PATH/disk_usage_before.txt"
du -sh "$AGENT_PATH"/* 2>/dev/null > "$BACKUP_PATH/agent_dirs_size_before.txt" || true

# Check agent service status
systemctl status vsts-agent-* 2>/dev/null > "$BACKUP_PATH/service_status_before.txt" || true
ps aux | grep -i agent > "$BACKUP_PATH/agent_processes_before.txt" || true

log_action "Backup completed"

# Clear cache if requested
if [ "$CLEAR_CACHE" = true ]; then
    print_status "Clearing Azure DevOps agent cache..."
    
    # Stop agent service first (if running)
    if systemctl is-active --quiet vsts-agent-* 2>/dev/null; then
        print_status "Stopping agent service..."
        sudo systemctl stop vsts-agent-* || true
        log_action "Agent service stopped"
        sleep 5
    fi
    
    # Clear cache directories
    CACHE_DIRS=(
        "_work/_tool"
        "_work/_temp" 
        "_work/_tasks"
        "_work/_download"
        "_work/_PipelineMapping"
        "_work/r1"
        "_work/1"
    )
    
    for dir in "${CACHE_DIRS[@]}"; do
        FULL_PATH="$AGENT_PATH/$dir"
        if [ -d "$FULL_PATH" ]; then
            print_status "Clearing $dir..."
            rm -rf "$FULL_PATH"/* 2>/dev/null || true
            log_action "Cleared cache directory: $dir"
        fi
    done
    
    # Clear specific project caches
    if [ -d "$AGENT_PATH/_work" ]; then
        find "$AGENT_PATH/_work" -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
        find "$AGENT_PATH/_work" -name "vendor" -type d -exec rm -rf {} + 2>/dev/null || true
        find "$AGENT_PATH/_work" -name "*.tar" -type f -delete 2>/dev/null || true
        log_action "Cleared project-specific caches"
    fi
    
    print_success "Cache clearing completed"
fi

# Restart service if requested
if [ "$RESTART_SERVICE" = true ]; then
    print_status "Restarting Azure DevOps agent service..."
    
    # Start agent service
    sudo systemctl start vsts-agent-* || true
    sleep 10
    
    # Check if service started successfully
    if systemctl is-active --quiet vsts-agent-* 2>/dev/null; then
        print_success "Agent service restarted successfully"
        log_action "Agent service restarted successfully"
    else
        print_error "Failed to restart agent service"
        log_action "ERROR: Failed to restart agent service"
    fi
fi

# Post-maintenance verification
print_status "Performing post-maintenance verification..."

# Get disk usage after
df -h > "$BACKUP_PATH/disk_usage_after.txt"
du -sh "$AGENT_PATH"/* 2>/dev/null > "$BACKUP_PATH/agent_dirs_size_after.txt" || true

# Check agent service status after
systemctl status vsts-agent-* 2>/dev/null > "$BACKUP_PATH/service_status_after.txt" || true
ps aux | grep -i agent > "$BACKUP_PATH/agent_processes_after.txt" || true

# Calculate space freed
SPACE_BEFORE=$(du -sb "$AGENT_PATH" 2>/dev/null | cut -f1 || echo "0")
SPACE_AFTER=$(du -sb "$AGENT_PATH" 2>/dev/null | cut -f1 || echo "0")
SPACE_FREED=$((SPACE_BEFORE - SPACE_AFTER))

if [ $SPACE_FREED -gt 0 ]; then
    SPACE_FREED_MB=$((SPACE_FREED / 1024 / 1024))
    print_success "Space freed: ${SPACE_FREED_MB} MB"
    log_action "Space freed: ${SPACE_FREED_MB} MB"
fi

log_action "Maintenance completed successfully"

print_success "Azure DevOps agent cache maintenance completed!"
print_status "Backup and logs saved to: $BACKUP_PATH"
print_status "Check agent_maintenance.log for detailed information"

# Final recommendations
echo ""
print_status "Next Steps:"
echo "1. Test a deployment to verify the agent is working correctly"
echo "2. Monitor the first few deployments for any issues"
echo "3. Check that fresh artifacts are being downloaded"
echo "4. Verify migration errors are resolved"

exit 0
