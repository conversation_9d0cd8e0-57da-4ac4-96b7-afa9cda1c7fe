# BardAccess Deployment Infrastructure Crisis - Complete Investigation & Resolution Guide

**Author**: <PERSON><PERSON><PERSON><PERSON>  
**Date**: July 22, 2025  
**Status**: Active Investigation  
**Priority**: Critical  

---

## Overview

After weeks of troubleshooting what appeared to be Laravel migration errors, I've discovered that our deployment failures are actually caused by a complete breakdown of our Azure DevOps agent infrastructure. This wiki documents my investigation, findings, and the path to resolution.

**TL;DR**: Our deployments have been serving 6+ month old cached files since June 17th due to Microsoft's Azure DevOps infrastructure changes. Every "migration error" we've been seeing is actually the system trying to run old migration files that we removed from our codebase weeks ago.

---

## Problem Statement

### What We Thought Was Happening
- Laravel migration errors during deployment
- `eifu_lot_numbers_software` table creation conflicts
- Database schema issues

### What's Actually Happening
- Azure DevOps agent cache corruption since June 2024
- Deployments using stale cached files instead of current builds
- Infrastructure serving files from February-July 2024 timeframe
- Build process succeeds but deployment uses wrong artifacts

---

## Investigation Timeline

### Phase 1: Initial Troubleshooting (July 10-17)
I started by addressing what seemed like straightforward migration conflicts:

**July 10** - Commit `c2f8c60`: Removed all `eifu_lot_numbers_software` migration files
- **Expected**: Migration errors would stop
- **Result**: Same errors continued (should have been my first clue)

**July 17** - Multiple urgent fixes:
- Commit `6e56462`: Added migration to mark problematic migrations as executed
- Commit `5d879e0`: Added aggressive debugging and cleanup
- Commit `8aa94e2`: Added automated database fix scripts
- Commit `fd24321`: Added Laravel Artisan command solution

**Result**: Nothing worked, which made no sense since the migration files were gone.

### Phase 2: Infrastructure Discovery (July 22)
The breakthrough came when our stakeholder escalated to the infrastructure team. The support conversation revealed:

- **Last successful deployment**: June 17, 2024
- **Agent cache location**: `/home/<USER>/agent1/_work/` unchanged since August 2023
- **Server mismatch**: Agent on `pl01bdh6adoap1`, deployment to `pl01mds6nckqap1`
- **Zombie processes**: "agent1" processes running from non-existent directories

---

## Root Cause Analysis

### The Perfect Storm: Microsoft Infrastructure Changes

**June 3-25, 2024**: Multiple Azure pipeline infrastructure updates
- Kristoph Herron made several "azure updates" commits
- Microsoft rolled out Azure DevOps agent deprecation changes
- Our agent cache system broke during this transition

**Evidence from Microsoft**:
> "Ubuntu-20.04 retirement date for Microsoft-hosted agents has been moved to April 30. On Managed DevOps Pools, the ubuntu-20.04 image will be available up to June 30."

### Why Migration Errors Appeared

```
Current Codebase (July 22): NO migration files ✅
Cached Deployment (June): OLD migration files ❌
Database (Current): Table already exists ✅
Result: Old migration tries to create existing table → ERROR
```

The system was literally trying to run code that we deleted weeks ago!

---

## Technical Evidence

### File System Analysis
From the infrastructure team's investigation:
```bash
# Agent cache files (all stale)
-rw-r--r-- 1 <USER> <GROUP> 4096 Feb 24 08:16 node_modules
-rw-r--r-- 1 <USER> <GROUP> 5492 Apr 23 18:09 package-lock.json
# No files newer than July 2024
```

### Commit History Proof
```bash
# Migration file was removed July 10
c2f8c60: "Remove all software lot numbers migration files - table already exists in production"

# Current status: NO migration files exist
find database/migrations -name "*eifu_lot_numbers_software*" -type f
# Returns: (empty)
```

### Infrastructure Issues
- **bdadmin last login**: June 23, 2024 (right when things broke)
- **Agent processes**: Running from directories that don't exist
- **Cache invalidation**: Completely broken since June infrastructure changes

---

## Current System State

### ✅ What's Working
- **Build Process**: Azure DevOps builds succeed
- **Artifact Creation**: Current files are generated correctly
- **Application Code**: All our fixes are technically sound
- **Database**: Production database is healthy

### ❌ What's Broken
- **Agent Cache**: Serving 6+ month old files
- **Deployment Pipeline**: Using stale cached artifacts
- **Server Communication**: Agent and deployment server routing issues
- **Cache Invalidation**: Not working since June 2024

---

## Resolution Plan

### Immediate Actions (Infrastructure Team)
I've coordinated with Tyler Durfee (UNIX team) for these critical fixes:

1. **Clear Agent Cache**
   - Target: `/home/<USER>/agent1/_work/` directory
   - Action: Complete cache invalidation and cleanup

2. **Fix Server Routing**
   - Resolve communication between `pl01bdh6adoap1` and `pl01mds6nckqap1`
   - Ensure agent registration works correctly

3. **Restart Azure DevOps Agents**
   - Clear cached artifacts in agent memory
   - Force re-download of current build artifacts

### Verification Steps
Once infrastructure fixes are complete:

1. **Test Deployment**: Should succeed without migration errors
2. **Verify Current Code**: Confirm we're deploying actual current codebase
3. **Monitor Stability**: Watch for 24-48 hours to ensure no regression

### Cleanup Actions (My Tasks)
After confirming the fix works:

1. **Remove Workaround Code**:
   - `app/Console/Commands/FixDeploymentDatabase.php`
   - Any deployment cleanup scripts
   - Migration marking scripts

2. **Update Documentation**: This wiki page and deployment procedures
3. **Implement Monitoring**: Add checks to detect future cache issues

---

## Lessons Learned

### What I Learned About Infrastructure Dependencies
- **Infrastructure issues can masquerade as application bugs**
- **Cache invalidation failures create phantom problems**
- **External service changes can silently break internal systems**
- **Always verify that deployments are actually using current code**

### Process Improvements
- **Escalate to infrastructure teams earlier** when fixes don't work as expected
- **Implement cache age monitoring** for deployment artifacts
- **Set up alerts for infrastructure changes** from cloud providers
- **Create deployment artifact verification** to ensure current code deployment

### Technical Insights
- **Silent failures are more dangerous than obvious ones**
- **Build success ≠ deployment success**
- **Cache corruption can persist for months undetected**
- **Server routing issues can cause deployment mismatches**

---

## Communication Templates

### For Stakeholders
*"After extensive investigation, I've determined this is an infrastructure issue, not a development problem. Microsoft's Azure DevOps changes in June 2024 broke our deployment cache system. Our builds succeed, but deployments use 6+ month old cached files. The infrastructure team is clearing the caches and fixing server routing. Once resolved, all migration errors should disappear since we removed those files weeks ago."*

### For Infrastructure Team
*"I can provide development context for the infrastructure fixes. The migration errors are phantom issues caused by stale cache serving old code. Once the agent cache is cleared, deployments should work normally since our current codebase has no migration conflicts."*

---

## Reference Information

### Key Commits
- **c2f8c60**: Removed problematic migration files (July 10)
- **fd24321**: Latest workaround attempt (July 17)
- **Current HEAD**: Clean codebase with no migration issues

### Infrastructure Details
- **Agent Server**: `pl01bdh6adoap1`
- **Deployment Server**: `pl01mds6nckqap1`
- **Cache Location**: `/home/<USER>/agent1/_work/`
- **Deployment Target**: `/data/vhosts/deployments/staging.bardaccess.com/`

### External References
- [Microsoft Azure DevOps Agent Updates](https://devblogs.microsoft.com/devops/upcoming-updates-for-azure-pipelines-agents-images/)
- [Azure DevOps Release Notes](https://learn.microsoft.com/en-us/azure/devops/release-notes/features-timeline-released)

---

## Action Items

### 🔥 Critical (Infrastructure Team)
- [ ] Clear agent cache on all servers
- [ ] Fix server routing between agent and deployment
- [ ] Restart Azure DevOps agents
- [ ] Verify deployment paths and permissions

### 📋 High Priority (My Tasks)
- [ ] Test deployment after infrastructure fixes
- [ ] Remove workaround code once confirmed working
- [ ] Update deployment documentation
- [ ] Implement cache monitoring

### 📈 Future Improvements
- [ ] Establish infrastructure change notification process
- [ ] Implement deployment pipeline health monitoring
- [ ] Create escalation procedures for infrastructure issues
- [ ] Document this incident for future reference

---

## Status Updates

**July 22, 2025**: Infrastructure team investigating. Waiting for cache clearing and server routing fixes.

**Next Update**: Will update this page once infrastructure fixes are implemented and tested.

---

> **Note**: This investigation took weeks to uncover because the symptoms (migration errors) were completely misleading. The real issue was infrastructure-level cache corruption that made it appear we had application problems. Always verify that your deployments are actually using current code!

**Last Updated**: July 22, 2025 by Chaithanya Bonthala
