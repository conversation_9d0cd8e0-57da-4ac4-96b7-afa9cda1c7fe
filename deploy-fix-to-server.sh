#!/bin/bash

# Deploy Azure DevOps Agent Cache Fix to Server
# This script will copy the maintenance scripts to the server and execute them
# Author: <PERSON><PERSON><PERSON><PERSON>
# Date: July 22, 2025

set -e

# Configuration
SERVER="dl01mds6nckqap1.bdx.com"
USER="bdladmin"
AGENT_PATH="/home/<USER>/agent1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Azure DevOps Agent Cache Fix Deployment"
print_status "Target Server: $SERVER"
echo ""

# Check if files exist locally
REQUIRED_FILES=(
    "clear-azure-devops-agent-cache.sh"
    "verify-agent-status.sh"
    "Azure_DevOps_Agent_Maintenance_Guide.md"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Required file not found: $file"
        exit 1
    fi
done

print_success "All required files found locally"

# Copy files to server
print_status "Copying maintenance scripts to server..."
scp clear-azure-devops-agent-cache.sh verify-agent-status.sh Azure_DevOps_Agent_Maintenance_Guide.md ${USER}@${SERVER}:~/

if [ $? -eq 0 ]; then
    print_success "Files copied successfully"
else
    print_error "Failed to copy files to server"
    exit 1
fi

# Execute maintenance on server
print_status "Executing cache maintenance on server..."

ssh ${USER}@${SERVER} << 'EOF'
    # Make scripts executable
    chmod +x clear-azure-devops-agent-cache.sh
    chmod +x verify-agent-status.sh
    
    echo "Starting Azure DevOps agent cache maintenance..."
    
    # Run cache clearing with full options
    sudo ./clear-azure-devops-agent-cache.sh -a /home/<USER>/agent1 -c -r -v
    
    echo ""
    echo "Verifying agent status after maintenance..."
    
    # Verify agent status
    ./verify-agent-status.sh -a /home/<USER>/agent1 -v
    
    echo ""
    echo "=== MAINTENANCE COMPLETED ==="
    echo "Next steps:"
    echo "1. Test a deployment to verify the fix"
    echo "2. Monitor deployment logs for fresh artifact downloads"
    echo "3. Confirm migration errors are resolved"
EOF

if [ $? -eq 0 ]; then
    print_success "Cache maintenance completed successfully!"
    echo ""
    print_status "The Azure DevOps agent cache has been cleared and the service restarted."
    print_status "Your deployment failures should now be resolved."
    echo ""
    print_status "Recommended next steps:"
    echo "1. Trigger a test deployment in Azure DevOps"
    echo "2. Monitor the deployment to ensure fresh artifacts are downloaded"
    echo "3. Verify that migration errors no longer occur"
    echo "4. Check that the deployment completes successfully"
else
    print_error "Cache maintenance failed"
    echo ""
    print_status "Troubleshooting steps:"
    echo "1. Check SSH connectivity to the server"
    echo "2. Verify you have sudo permissions on the server"
    echo "3. Review the maintenance logs on the server"
    echo "4. Try running the scripts manually on the server"
fi

exit 0
