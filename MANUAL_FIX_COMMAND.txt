#!/bin/bash
# Azure DevOps Agent Cache Fix - Single Command
# Copy and paste this entire command on dl01mds6nckqap1.bdx.com

echo "=== Starting Azure DevOps Agent Cache Fix ===" && \
echo "Stopping agent service..." && \
sudo systemctl stop vsts-agent-* && \
sleep 5 && \
echo "Clearing corrupted cache directories..." && \
sudo rm -rf /home/<USER>/agent1/_work/_tool/* 2>/dev/null && \
sudo rm -rf /home/<USER>/agent1/_work/_temp/* 2>/dev/null && \
sudo rm -rf /home/<USER>/agent1/_work/_tasks/* 2>/dev/null && \
sudo rm -rf /home/<USER>/agent1/_work/_download/* 2>/dev/null && \
sudo rm -rf /home/<USER>/agent1/_work/_PipelineMapping/* 2>/dev/null && \
sudo rm -rf /home/<USER>/agent1/_work/r1/* 2>/dev/null && \
sudo rm -rf /home/<USER>/agent1/_work/1/* 2>/dev/null && \
find /home/<USER>/agent1/_work -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null && \
find /home/<USER>/agent1/_work -name "vendor" -type d -exec rm -rf {} + 2>/dev/null && \
find /home/<USER>/agent1/_work -name "*.tar" -type f -delete 2>/dev/null && \
echo "Cache cleared successfully!" && \
echo "Restarting agent service..." && \
sudo systemctl start vsts-agent-* && \
sleep 10 && \
echo "Verifying agent status..." && \
systemctl status vsts-agent-* --no-pager && \
echo "" && \
echo "=== FIX COMPLETED SUCCESSFULLY ===" && \
echo "✅ Agent cache cleared of June 2025 stale files" && \
echo "✅ Agent service restarted" && \
echo "✅ Ready for fresh deployments" && \
echo "" && \
echo "Next steps:" && \
echo "1. Trigger a test deployment in Azure DevOps" && \
echo "2. Verify migration errors are resolved" && \
echo "3. Confirm fresh artifacts are downloaded"
