#!/bin/bash

# Run this script directly on dl01mds6nckqap1.bdx.com
# This will fix the Azure DevOps agent cache corruption issue
# Author: <PERSON><PERSON><PERSON><PERSON>
# Date: July 22, 2025

set -e

# Configuration
AGENT_PATH="/home/<USER>/agent1"
BACKUP_PATH="/home/<USER>/cache_backup_$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_action() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$BACKUP_PATH/agent_maintenance.log"
    print_status "$1"
}

print_status "=== Azure DevOps Agent Cache Fix ==="
print_status "Server: $(hostname)"
print_status "Agent Path: $AGENT_PATH"
print_status "Backup Path: $BACKUP_PATH"
echo ""

# Check if running as appropriate user
if [ "$(whoami)" != "bdladmin" ] && [ "$EUID" -ne 0 ]; then
    print_warning "Consider running as bdladmin or with sudo for full access"
fi

# Validate agent path exists
if [ ! -d "$AGENT_PATH" ]; then
    print_error "Agent path does not exist: $AGENT_PATH"
    exit 1
fi

# Create backup directory
mkdir -p "$BACKUP_PATH"
log_action "Starting Azure DevOps agent cache maintenance"

# Backup current state
print_status "Creating backup of current state..."
find "$AGENT_PATH/_work" -type d 2>/dev/null | head -100 > "$BACKUP_PATH/work_directory_structure.txt" || true
df -h > "$BACKUP_PATH/disk_usage_before.txt"
du -sh "$AGENT_PATH"/* 2>/dev/null > "$BACKUP_PATH/agent_dirs_size_before.txt" || true
systemctl status vsts-agent-* 2>/dev/null > "$BACKUP_PATH/service_status_before.txt" || true
ps aux | grep -i agent > "$BACKUP_PATH/agent_processes_before.txt" || true

log_action "Backup completed"

# Stop agent service
print_status "Stopping Azure DevOps agent service..."
if systemctl is-active --quiet vsts-agent-* 2>/dev/null; then
    sudo systemctl stop vsts-agent-* || true
    log_action "Agent service stopped"
    sleep 5
else
    log_action "Agent service was not running"
fi

# Clear cache directories
print_status "Clearing corrupted cache directories..."

CACHE_DIRS=(
    "_work/_tool"
    "_work/_temp" 
    "_work/_tasks"
    "_work/_download"
    "_work/_PipelineMapping"
    "_work/r1"
    "_work/1"
)

for dir in "${CACHE_DIRS[@]}"; do
    FULL_PATH="$AGENT_PATH/$dir"
    if [ -d "$FULL_PATH" ]; then
        print_status "Clearing $dir..."
        rm -rf "$FULL_PATH"/* 2>/dev/null || true
        log_action "Cleared cache directory: $dir"
    fi
done

# Clear specific project caches
if [ -d "$AGENT_PATH/_work" ]; then
    print_status "Clearing project-specific caches..."
    find "$AGENT_PATH/_work" -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
    find "$AGENT_PATH/_work" -name "vendor" -type d -exec rm -rf {} + 2>/dev/null || true
    find "$AGENT_PATH/_work" -name "*.tar" -type f -delete 2>/dev/null || true
    log_action "Cleared project-specific caches"
fi

print_success "Cache clearing completed"

# Restart agent service
print_status "Restarting Azure DevOps agent service..."
sudo systemctl start vsts-agent-* || true
sleep 10

# Verify service started
if systemctl is-active --quiet vsts-agent-* 2>/dev/null; then
    print_success "Agent service restarted successfully"
    log_action "Agent service restarted successfully"
else
    print_error "Failed to restart agent service"
    log_action "ERROR: Failed to restart agent service"
fi

# Post-maintenance verification
print_status "Performing post-maintenance verification..."

# Get disk usage after
df -h > "$BACKUP_PATH/disk_usage_after.txt"
du -sh "$AGENT_PATH"/* 2>/dev/null > "$BACKUP_PATH/agent_dirs_size_after.txt" || true
systemctl status vsts-agent-* 2>/dev/null > "$BACKUP_PATH/service_status_after.txt" || true
ps aux | grep -i agent > "$BACKUP_PATH/agent_processes_after.txt" || true

# Calculate space freed
SPACE_BEFORE=$(cat "$BACKUP_PATH/agent_dirs_size_before.txt" | awk '{sum += $1} END {print sum}' 2>/dev/null || echo "0")
SPACE_AFTER=$(du -sh "$AGENT_PATH" 2>/dev/null | awk '{print $1}' || echo "0")

print_success "Space analysis completed"
log_action "Maintenance completed successfully"

# Final verification checks
echo ""
print_status "=== FINAL VERIFICATION ==="

# Check 1: Agent service
if systemctl is-active --quiet vsts-agent-* 2>/dev/null; then
    print_success "✓ Agent service is running"
else
    print_error "✗ Agent service is not running"
fi

# Check 2: Agent processes
AGENT_PROCESSES=$(ps aux | grep -i "Agent.Listener\|Agent.Worker" | grep -v grep | wc -l)
if [ $AGENT_PROCESSES -gt 0 ]; then
    print_success "✓ Found $AGENT_PROCESSES agent process(es)"
else
    print_warning "⚠ No agent processes found"
fi

# Check 3: Work directory
if [ -d "$AGENT_PATH/_work" ]; then
    print_success "✓ Work directory exists"
else
    print_warning "⚠ Work directory not found (will be created on first run)"
fi

# Check 4: Network connectivity
if ping -c 1 dev.azure.com >/dev/null 2>&1; then
    print_success "✓ Can reach Azure DevOps"
else
    print_error "✗ Cannot reach Azure DevOps"
fi

echo ""
print_success "=== MAINTENANCE COMPLETED ==="
print_status "Backup and logs saved to: $BACKUP_PATH"
print_status "Check agent_maintenance.log for detailed information"

echo ""
print_status "=== NEXT STEPS ==="
echo "1. 🚀 Trigger a test deployment in Azure DevOps"
echo "2. 👀 Monitor deployment logs for fresh artifact downloads"
echo "3. ✅ Verify migration errors are resolved"
echo "4. 🎉 Confirm deployment completes successfully"

echo ""
print_status "The corrupted cache from June 2025 has been cleared."
print_status "Fresh artifacts will be downloaded on the next deployment."
print_status "Migration errors should be completely resolved."

exit 0
