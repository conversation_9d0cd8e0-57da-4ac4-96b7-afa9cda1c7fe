# 🚀 EXECUTE THE AZURE DEVOPS AGENT CACHE FIX

## Current Situation
- ✅ **All fix scripts are ready** in your local directory
- ❌ **Direct network access** to `dl01mds6nckqap1.bdx.com` is not available from your machine
- 🎯 **Goal**: Clear the corrupted agent cache to fix deployment failures

---

## 🎯 OPTION 1: Use Your Stakeholder's Access (RECOMMENDED)

Since your stakeholder mentioned they can access the server via VPN, this is the fastest approach:

### Step 1: Send Scripts to Stakeholder
Send these files to your stakeholder who has VPN access:
- `run-on-server-directly.sh` (the complete fix script)
- `Azure_DevOps_Agent_Maintenance_Guide.md` (documentation)

### Step 2: Stakeholder Instructions
Tell them to:
```bash
# 1. Connect to VPN
# 2. SSH to the server
ssh <EMAIL>

# 3. Create and run the fix script
cat > fix-agent-cache.sh << 'EOF'
[Paste the entire content of run-on-server-directly.sh here]
EOF

chmod +x fix-agent-cache.sh
sudo ./fix-agent-cache.sh
```

---

## 🎯 OPTION 2: Azure DevOps Pipeline Approach

Create a special Azure DevOps pipeline to fix the agent cache:

### Step 1: Create Pipeline YAML
```yaml
# azure-pipelines-fix-agent.yml
trigger: none  # Manual trigger only

pool:
  name: 'Default'  # Use the problematic agent pool

steps:
- script: |
    echo "Fixing Azure DevOps Agent Cache"
    sudo systemctl stop vsts-agent-* || true
    sudo rm -rf /home/<USER>/agent1/_work/_tool/* || true
    sudo rm -rf /home/<USER>/agent1/_work/_temp/* || true
    sudo rm -rf /home/<USER>/agent1/_work/_tasks/* || true
    sudo rm -rf /home/<USER>/agent1/_work/_download/* || true
    sudo rm -rf /home/<USER>/agent1/_work/_PipelineMapping/* || true
    sudo systemctl start vsts-agent-* || true
    systemctl status vsts-agent-*
  displayName: 'Clear Agent Cache'
```

### Step 2: Run the Pipeline
1. Add this YAML to your Azure DevOps project
2. Run the pipeline manually
3. This will execute directly on the problematic agent and fix itself

---

## 🎯 OPTION 3: Infrastructure Team Request

### Email Template for Infrastructure Team:
```
Subject: URGENT: Azure DevOps Agent Cache Clearing Required - dl01mds6nckqap1

Hi Infrastructure Team,

We've identified that our deployment failures are caused by corrupted agent cache on dl01mds6nckqap1.bdx.com. 

ISSUE: The agent is serving stale cached files from June 2025 instead of current builds, causing migration errors for files that no longer exist.

SOLUTION NEEDED: Please execute these commands on dl01mds6nckqap1.bdx.com:

```bash
# Stop agent service
sudo systemctl stop vsts-agent-*

# Clear corrupted cache directories
sudo rm -rf /home/<USER>/agent1/_work/_tool/*
sudo rm -rf /home/<USER>/agent1/_work/_temp/*
sudo rm -rf /home/<USER>/agent1/_work/_tasks/*
sudo rm -rf /home/<USER>/agent1/_work/_download/*
sudo rm -rf /home/<USER>/agent1/_work/_PipelineMapping/*

# Restart agent service
sudo systemctl start vsts-agent-*

# Verify agent is running
systemctl status vsts-agent-*
```

EXPECTED RESULT: After this maintenance, deployments will download fresh artifacts and migration errors will be resolved.

TIME REQUIRED: 5-10 minutes
IMPACT: Deployments will be unavailable during maintenance

Please confirm when this can be scheduled.

Thanks,
Chaithanya Bonthala
```

---

## 🎯 OPTION 4: Manual Command Execution

If you can get any access to the server (through any method), just run this single command:

```bash
sudo systemctl stop vsts-agent-*; sudo rm -rf /home/<USER>/agent1/_work/_tool/* /home/<USER>/agent1/_work/_temp/* /home/<USER>/agent1/_work/_tasks/* /home/<USER>/agent1/_work/_download/* /home/<USER>/agent1/_work/_PipelineMapping/* 2>/dev/null; sudo systemctl start vsts-agent-*; systemctl status vsts-agent-*
```

---

## 📋 What Happens After the Fix

1. **Immediate**: Agent cache is cleared of June 2025 stale files
2. **Next Deployment**: Fresh artifacts downloaded from current builds
3. **Result**: Migration errors disappear (no conflicting files in current code)
4. **Status**: Normal deployment process restored

---

## 🔍 How to Verify the Fix Worked

After the cache clearing:

1. **Trigger a test deployment** in Azure DevOps
2. **Check deployment logs** for:
   - Fresh artifact downloads
   - No migration errors about `eifu_lot_numbers_software`
   - Successful deployment completion
3. **Confirm** the deployment uses current codebase (no migration files)

---

## 📞 Next Steps

**Choose the option that works best for your access situation:**

- ✅ **Option 1**: Fastest if stakeholder has VPN access
- ✅ **Option 2**: Clever if Azure DevOps is accessible
- ✅ **Option 3**: Professional if infrastructure team handles it
- ✅ **Option 4**: Direct if you get any server access

**All options achieve the same result: clearing the corrupted cache and fixing deployments.**

---

## 🎉 Expected Outcome

After executing any of these options:
- ✅ **Deployment failures resolved**
- ✅ **Migration errors eliminated** 
- ✅ **Fresh artifacts downloaded**
- ✅ **Normal deployment process restored**

**The corrupted June 2025 cache will be completely cleared and your deployments will work normally again!**
