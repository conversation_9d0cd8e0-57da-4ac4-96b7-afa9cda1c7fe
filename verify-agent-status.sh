#!/bin/bash

# Azure DevOps Agent Status Verification Script
# Purpose: Verify agent health after cache maintenance
# Author: <PERSON><PERSON><PERSON><PERSON>
# Date: July 22, 2025

set -e

# Default values
AGENT_PATH="/home/<USER>/agent1"
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -a PATH    Agent path (default: /home/<USER>/agent1)"
    echo "  -v         Verbose output"
    echo "  -h         Show this help message"
}

while getopts "a:vh" opt; do
    case $opt in
        a) AGENT_PATH="$OPTARG" ;;
        v) VERBOSE=true ;;
        h) show_usage; exit 0 ;;
        \?) echo "Invalid option -$OPTARG" >&2; show_usage; exit 1 ;;
    esac
done

print_status "Azure DevOps Agent Status Verification"
print_status "Agent Path: $AGENT_PATH"
echo ""

# Check 1: Agent directory exists
if [ -d "$AGENT_PATH" ]; then
    print_success "Agent directory exists: $AGENT_PATH"
else
    print_error "Agent directory not found: $AGENT_PATH"
    exit 1
fi

# Check 2: Agent service status
print_status "Checking agent service status..."
if systemctl is-active --quiet vsts-agent-* 2>/dev/null; then
    print_success "Agent service is running"
    if [ "$VERBOSE" = true ]; then
        systemctl status vsts-agent-* --no-pager
    fi
else
    print_error "Agent service is not running"
    echo "Try: sudo systemctl start vsts-agent-*"
fi

# Check 3: Agent processes
print_status "Checking agent processes..."
AGENT_PROCESSES=$(ps aux | grep -i "Agent.Listener\|Agent.Worker" | grep -v grep | wc -l)
if [ $AGENT_PROCESSES -gt 0 ]; then
    print_success "Found $AGENT_PROCESSES agent process(es)"
    if [ "$VERBOSE" = true ]; then
        ps aux | grep -i "Agent.Listener\|Agent.Worker" | grep -v grep
    fi
else
    print_warning "No agent processes found"
fi

# Check 4: Disk space
print_status "Checking disk space..."
DISK_USAGE=$(df -h "$AGENT_PATH" | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 90 ]; then
    print_success "Disk space OK: ${DISK_USAGE}% used"
else
    print_warning "Disk space high: ${DISK_USAGE}% used"
fi

# Check 5: Agent configuration
print_status "Checking agent configuration..."
if [ -f "$AGENT_PATH/.agent" ]; then
    print_success "Agent configuration file exists"
    if [ "$VERBOSE" = true ]; then
        echo "Agent details:"
        cat "$AGENT_PATH/.agent" | grep -E "agentName|poolName|serverUrl" || true
    fi
else
    print_error "Agent configuration file missing"
fi

# Check 6: Work directory structure
print_status "Checking work directory structure..."
if [ -d "$AGENT_PATH/_work" ]; then
    print_success "Work directory exists"
    WORK_DIRS=$(find "$AGENT_PATH/_work" -maxdepth 1 -type d | wc -l)
    print_status "Work subdirectories: $((WORK_DIRS - 1))"
else
    print_warning "Work directory not found (will be created on first run)"
fi

# Check 7: Recent logs
print_status "Checking recent agent logs..."
LOG_DIR="$AGENT_PATH/_diag"
if [ -d "$LOG_DIR" ]; then
    RECENT_LOGS=$(find "$LOG_DIR" -name "*.log" -mtime -1 | wc -l)
    if [ $RECENT_LOGS -gt 0 ]; then
        print_success "Found $RECENT_LOGS recent log file(s)"
        if [ "$VERBOSE" = true ]; then
            echo "Most recent log entries:"
            find "$LOG_DIR" -name "*.log" -mtime -1 -exec tail -5 {} \; 2>/dev/null | tail -10
        fi
    else
        print_warning "No recent log files found"
    fi
else
    print_warning "Log directory not found"
fi

# Check 8: Network connectivity
print_status "Checking network connectivity..."
if ping -c 1 dev.azure.com >/dev/null 2>&1; then
    print_success "Can reach Azure DevOps (dev.azure.com)"
else
    print_error "Cannot reach Azure DevOps (dev.azure.com)"
fi

# Check 9: Agent capabilities
print_status "Checking agent capabilities..."
if [ -f "$AGENT_PATH/.capabilities" ]; then
    print_success "Agent capabilities file exists"
    CAPABILITIES_COUNT=$(wc -l < "$AGENT_PATH/.capabilities")
    print_status "Registered capabilities: $CAPABILITIES_COUNT"
    if [ "$VERBOSE" = true ]; then
        echo "Sample capabilities:"
        head -10 "$AGENT_PATH/.capabilities"
    fi
else
    print_warning "Agent capabilities file not found"
fi

# Summary
echo ""
print_status "=== VERIFICATION SUMMARY ==="

# Overall health check
ISSUES=0

if ! systemctl is-active --quiet vsts-agent-* 2>/dev/null; then
    ((ISSUES++))
fi

if [ $DISK_USAGE -gt 90 ]; then
    ((ISSUES++))
fi

if [ ! -f "$AGENT_PATH/.agent" ]; then
    ((ISSUES++))
fi

if ! ping -c 1 dev.azure.com >/dev/null 2>&1; then
    ((ISSUES++))
fi

if [ $ISSUES -eq 0 ]; then
    print_success "Agent appears to be healthy and ready for deployments"
    echo ""
    echo "Recommended next steps:"
    echo "1. Trigger a test deployment"
    echo "2. Monitor the deployment logs"
    echo "3. Verify fresh artifacts are downloaded"
    echo "4. Confirm migration errors are resolved"
else
    print_warning "Found $ISSUES potential issue(s) that may need attention"
    echo ""
    echo "Review the checks above and address any errors before testing deployments"
fi

exit 0
