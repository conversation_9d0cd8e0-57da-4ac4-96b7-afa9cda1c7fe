<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('eifu_lot_numbers_software', function (Blueprint $table) {
            $table->id();
            $table->string('lot_number')->index();
            $table->string('ref')->nullable();
            $table->string('product_code')->nullable();
            $table->string('product_name')->nullable();
            $table->string('part_number')->nullable();
            $table->string('resource_type')->nullable();
            $table->date('manufacture_date')->nullable();
            $table->integer('sequence_number')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            // Add indexes for better performance
            $table->index('lot_number');
            $table->index('part_number');
            $table->index('product_code');
            $table->index('manufacture_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('eifu_lot_numbers_software');
    }
};