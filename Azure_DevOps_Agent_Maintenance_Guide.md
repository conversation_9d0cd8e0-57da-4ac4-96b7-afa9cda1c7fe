# Azure DevOps Agent Cache Maintenance Guide

**Server**: `dl01mds6nckqap1.bdx.com`  
**Issue**: Agent cache corruption causing deployment failures  
**Solution**: Clear corrupted cache and restart agent service  

---

## Quick Start

### 1. Connect to Server
```bash
ssh <EMAIL>
```

### 2. Upload Scripts
Transfer the maintenance scripts to the server:
- `clear-azure-devops-agent-cache.sh`
- `verify-agent-status.sh`

### 3. Make Scripts Executable
```bash
chmod +x clear-azure-devops-agent-cache.sh
chmod +x verify-agent-status.sh
```

### 4. Run Cache Maintenance
```bash
# Full maintenance with backup, cache clearing, and service restart
sudo ./clear-azure-devops-agent-cache.sh -a /home/<USER>/agent1 -c -r -v
```

### 5. Verify Agent Status
```bash
./verify-agent-status.sh -a /home/<USER>/agent1 -v
```

---

## Detailed Steps

### Step 1: Pre-Maintenance Check
Before starting, verify current agent status:
```bash
# Check if agent service is running
systemctl status vsts-agent-*

# Check agent processes
ps aux | grep -i agent

# Check disk space
df -h /home/<USER>/agent1
```

### Step 2: Run Cache Clearing Script
The script will:
- Create automatic backups
- Stop the agent service
- Clear corrupted cache directories
- Restart the agent service
- Log all actions

```bash
sudo ./clear-azure-devops-agent-cache.sh -a /home/<USER>/agent1 -c -r -v
```

**Script Options:**
- `-a PATH`: Agent path (default: /home/<USER>/agent1)
- `-c`: Clear cache directories
- `-r`: Restart agent service
- `-v`: Verbose output

### Step 3: Verify Success
Run the verification script to ensure everything is working:
```bash
./verify-agent-status.sh -a /home/<USER>/agent1 -v
```

This checks:
- Agent service status
- Agent processes
- Disk space
- Network connectivity
- Configuration files

### Step 4: Test Deployment
After maintenance, test a deployment to verify:
- Fresh artifacts are downloaded
- Migration errors are resolved
- Deployment completes successfully

---

## What Gets Cleared

The maintenance script clears these cache directories:
- `_work/_tool` - Tool cache (SDKs, tools)
- `_work/_temp` - Temporary files
- `_work/_tasks` - Task definitions cache
- `_work/_download` - Downloaded artifacts
- `_work/_PipelineMapping` - Pipeline mappings
- `_work/r1` and `_work/1` - Build workspace caches
- `node_modules` directories (NPM caches)
- `vendor` directories (Composer caches)
- `*.tar` files (Artifact archives)

---

## Backup Information

Backups are automatically created in `/home/<USER>/cache_backup_TIMESTAMP/`:
- `agent_maintenance.log` - Detailed maintenance log
- `work_directory_structure.txt` - Directory structure before clearing
- `service_status_before.txt` - Service status before maintenance
- `service_status_after.txt` - Service status after maintenance
- `disk_usage_before.txt` - Disk usage before clearing
- `disk_usage_after.txt` - Disk usage after clearing

---

## Troubleshooting

### Agent Service Won't Start
```bash
# Check service status
sudo systemctl status vsts-agent-*

# Try manual start
sudo systemctl start vsts-agent-*

# Check logs
journalctl -u vsts-agent-* -f
```

### Permission Issues
```bash
# Fix ownership if needed
sudo chown -R bdadmin:bdadmin /home/<USER>/agent1

# Check permissions
ls -la /home/<USER>/agent1
```

### Still Getting Migration Errors
If you still get `eifu_lot_numbers_software` migration errors after clearing cache:
1. Verify the cache was actually cleared
2. Check that fresh artifacts are being downloaded
3. Confirm current codebase has no migration files
4. Review deployment logs for specific error details

---

## Communication Templates

### For Infrastructure Team
*"We need to perform Azure DevOps agent cache maintenance on dl01mds6nckqap1 to resolve deployment failures. The process involves clearing corrupted cache directories and restarting the agent service. Estimated time: 15-30 minutes. Deployments will be unavailable during this window."*

### For Stakeholders
*"We've identified the root cause of our deployment failures as corrupted agent cache on the deployment server. We have scripts ready to safely clear the cache and restore normal deployment functionality. This is an infrastructure maintenance task, not a code issue."*

### Post-Maintenance Update
*"Agent cache maintenance completed successfully. The corrupted cache has been cleared and the agent service restarted. Fresh artifacts are now being downloaded and deployments should work normally. Migration errors should be resolved as the current codebase has no conflicting migration files."*

---

## Expected Results

After successful maintenance:
- ✅ Agent cache cleared of stale files from June 2025
- ✅ Agent service running and healthy
- ✅ Fresh build artifacts downloaded on next deployment
- ✅ Migration errors resolved (no conflicting files in current code)
- ✅ Normal deployment process restored

---

## Prevention

To prevent future cache corruption:
1. **Regular Maintenance**: Schedule monthly cache clearing
2. **Monitoring**: Set up alerts for agent health and disk space
3. **Documentation**: Keep this guide updated with any changes
4. **Testing**: Test deployments after any infrastructure changes

---

## Support

If issues persist after following this guide:
1. Check the maintenance log for errors
2. Verify network connectivity to Azure DevOps
3. Confirm agent registration is valid
4. Contact infrastructure team with specific error messages

**Created**: July 22, 2025  
**Author**: Chaithanya Bonthala  
**Purpose**: Resolve Azure DevOps agent cache corruption on dl01mds6nckqap1
